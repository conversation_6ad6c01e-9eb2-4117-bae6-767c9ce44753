/ Header Record For PersistentHashMapValueStorage_ ^E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\data\Completion.ktb aE:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\data\CompletionDao.kti hE:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\data\CompletionRepository.ktZ YE:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\data\Habit.kt] \E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\data\HabitDao.ktb aE:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\data\HabitDatabase.ktd cE:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\data\HabitRepository.kta `E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\data\HabitSection.ktd cE:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\data\HabitSectionDao.ktk jE:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\data\HabitSectionRepository.ktn mE:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\data\UserPreferencesRepository.kta `E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\di\DatabaseModule.kt` _E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\ui\MainViewModel.kts rE:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\ui\createhabit\CreateHabitViewModel.kty xE:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\ui\managesections\ManageSectionsViewModel.ktm lE:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\ui\settings\SettingsViewModel.kt_ ^E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\uhabits_99\MainActivity.kte dE:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\java\com\example\habits9\ui\auth\AuthViewModel.kt
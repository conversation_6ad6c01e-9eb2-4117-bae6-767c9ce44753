package com.example.habits9.ui.auth

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseAuthException
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import javax.inject.Inject

data class AuthUiState(
    val email: String = "",
    val password: String = "",
    val isLoading: Boolean = false,
    val errorMessage: String = "",
    val isSignedIn: Boolean = false
)

@HiltViewModel
class AuthViewModel @Inject constructor() : ViewModel() {
    
    private val firebaseAuth = FirebaseAuth.getInstance()
    
    private val _uiState = MutableStateFlow(AuthUiState())
    val uiState: StateFlow<AuthUiState> = _uiState.asStateFlow()
    
    init {
        // Check if user is already signed in
        checkAuthState()
    }
    
    private fun checkAuthState() {
        val currentUser = firebaseAuth.currentUser
        _uiState.value = _uiState.value.copy(
            isSignedIn = currentUser != null
        )
    }
    
    fun updateEmail(email: String) {
        _uiState.value = _uiState.value.copy(
            email = email,
            errorMessage = "" // Clear error when user types
        )
    }
    
    fun updatePassword(password: String) {
        _uiState.value = _uiState.value.copy(
            password = password,
            errorMessage = "" // Clear error when user types
        )
    }
    
    fun signUp(onSuccess: () -> Unit) {
        val currentState = _uiState.value
        
        // Basic validation
        if (currentState.email.isBlank()) {
            _uiState.value = currentState.copy(errorMessage = "Email cannot be empty")
            return
        }
        
        if (currentState.password.isBlank()) {
            _uiState.value = currentState.copy(errorMessage = "Password cannot be empty")
            return
        }
        
        if (currentState.password.length < 6) {
            _uiState.value = currentState.copy(errorMessage = "Password must be at least 6 characters")
            return
        }
        
        _uiState.value = currentState.copy(isLoading = true, errorMessage = "")
        
        viewModelScope.launch {
            try {
                firebaseAuth.createUserWithEmailAndPassword(
                    currentState.email,
                    currentState.password
                ).await()
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    isSignedIn = true
                )
                onSuccess()
                
            } catch (e: FirebaseAuthException) {
                val errorMessage = when (e.errorCode) {
                    "ERROR_INVALID_EMAIL" -> "The email address is badly formatted."
                    "ERROR_EMAIL_ALREADY_IN_USE" -> "The email address is already in use by another account."
                    "ERROR_WEAK_PASSWORD" -> "The password is too weak."
                    else -> e.message ?: "Sign up failed. Please try again."
                }
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = errorMessage
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "An unexpected error occurred. Please try again."
                )
            }
        }
    }
    
    fun signIn(onSuccess: () -> Unit) {
        val currentState = _uiState.value
        
        // Basic validation
        if (currentState.email.isBlank()) {
            _uiState.value = currentState.copy(errorMessage = "Email cannot be empty")
            return
        }
        
        if (currentState.password.isBlank()) {
            _uiState.value = currentState.copy(errorMessage = "Password cannot be empty")
            return
        }
        
        _uiState.value = currentState.copy(isLoading = true, errorMessage = "")
        
        viewModelScope.launch {
            try {
                firebaseAuth.signInWithEmailAndPassword(
                    currentState.email,
                    currentState.password
                ).await()
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    isSignedIn = true
                )
                onSuccess()
                
            } catch (e: FirebaseAuthException) {
                val errorMessage = when (e.errorCode) {
                    "ERROR_INVALID_EMAIL" -> "The email address is badly formatted."
                    "ERROR_USER_NOT_FOUND" -> "No account found with this email address."
                    "ERROR_WRONG_PASSWORD" -> "The password is incorrect."
                    "ERROR_USER_DISABLED" -> "This account has been disabled."
                    else -> e.message ?: "Sign in failed. Please try again."
                }
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = errorMessage
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "An unexpected error occurred. Please try again."
                )
            }
        }
    }
    
    fun signOut(onSuccess: () -> Unit) {
        firebaseAuth.signOut()
        _uiState.value = _uiState.value.copy(
            email = "",
            password = "",
            isSignedIn = false,
            errorMessage = ""
        )
        onSuccess()
    }
    
    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = "")
    }
}

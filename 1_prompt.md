# 📝 Prompt: Implement Full Authentication Flow with UI Redesign

## 1. Objective

To implement a complete, secure, and user-friendly authentication system. This involves redesigning the authentication screen based on a new UI, and adding critical features: email verification, password recovery, and account deletion.

## 2. Part 1: Authentication Screen UI Redesign

* **Goal:** Rebuild the Sign In/Sign Up screen to match the new, professional design, replacing the old, unstyled version.
* **Visual & Structural References:**
    * The final design must match the layout, look, and feel shown in **`image_2dd91f.png`**.
    * The file **`Login.html`** is provided as a reference for the UI structure and styling elements.
* **Implementation Details:**
    * Create a single, unified screen for both Sign In and Sign Up. Use tabs or a similar toggle mechanism to allow the user to switch between the two forms.
    * The layout must be a centered vertical column containing:
        * The application logo at the top.
        * "Sign In" and "Sign Up" tabs.
        * An "Email" text input field.
        * A "Password" text input field.
        * A "Forgot Password?" text link, which should only be visible on the "Sign In" view.
        * A primary action button (e.g., "Sign In" or "Sign Up").
    * All styling (colors, fonts, spacing, component shapes) must be implemented according to the `style.md` design system, using the visual cues from the provided references.

## 3. Part 2: Implement Email Verification

* **Goal:** Ensure that new users verify their email address before they can fully use the app.
* **Implementation Details:**
    * **Sign-Up Logic:** Immediately after a `createUserWithEmailAndPassword()` call is successful, you must call the Firebase `sendEmailVerification()` function for the new user.
    * **New Verification Screen:** Create a new, simple screen (`VerificationScreen.kt`). This screen should display a clear message like, "Verification email sent to [user's email]. Please check your inbox and click the link to activate your account." It should also have a "Back to Login" button.
    * **Navigation:** After a new user signs up, navigate them to this new `VerificationScreen`.
    * **Login Logic Update:** Modify the sign-in logic. After a user successfully authenticates with `signInWithEmailAndPassword()`, you must check if their email is verified (`Firebase.auth.currentUser.isEmailVerified`).
        * If `true`, proceed to the main app (Home Screen).
        * If `false`, do not proceed. Instead, show an error message on the login screen, such as "Please verify your email before logging in."

## 4. Part 3: Implement Password Recovery

* **Goal:** Provide a way for users to reset their password if they forget it.
* **Implementation Details:**
    * Make the "Forgot Password?" text link on the Sign In screen functional.
    * When tapped, this link should open a simple dialog or a new screen that contains:
        * A text input field for the user to enter their email address.
        * A "Send Reset Email" button.
    * When the button is pressed, call the Firebase `sendPasswordResetEmail()` function with the provided email.
    * After the call, close the dialog/screen and show a temporary confirmation message (e.g., a Toast or Snackbar) on the login screen stating, "If an account exists for that email, a reset link has been sent."

## 5. Part 4: Implement Account Deletion

* **Goal:** Allow users to permanently delete their own account from within the app settings.
* **Implementation Details:**
    * **Location:** This feature must be implemented in the **Settings screen**, not the authentication screen.
    * **UI:** Add a "Delete Account" button to the `SettingsScreen.kt`. It should be styled to look like a destructive action (e.g., using a red color for the text or border) to distinguish it from other settings.
    * **Confirmation:** Tapping this button **must** open a confirmation dialog. The dialog should explicitly warn the user: "Are you sure you want to permanently delete your account? All of your data will be lost. This action cannot be undone." It must have "Cancel" and "Delete" buttons.
    * **Logic:** Only if the user taps "Delete" in the dialog, proceed to call `Firebase.auth.currentUser.delete()`.
        * Note: Firebase requires a recent login for this action to succeed. You must handle any `FirebaseAuthRecentLoginRequiredException` by gracefully prompting the user to re-enter their password before the deletion can be finalized.
    * **Navigation:** After a successful deletion, log the user out and navigate them back to the `AuthScreen`.

## 6. Verification / Testing Section

* **Test Case 1 (UI):** The new authentication screen must be visually identical to `image_2dd91f.png` and function correctly in both light and dark modes.
* **Test Case 2 (Verification):**
    * Sign up with a new email. Verify you are taken to the "Verification email sent" screen and that you receive an email.
    * Go back to the login screen and try to log in with the new, unverified account. Verify that you are shown an error and blocked from entering the app.
    * Click the link in the email. Now try to log in again. Verify that you are successful.
* **Test Case 3 (Password Reset):**
    * From the login screen, click "Forgot Password?".
    * Enter the email of a created account and submit. Verify that you receive a password reset email.
* **Test Case 4 (Deletion):**
    * Log in to the app and navigate to Settings.
    * Tap "Delete Account." Verify the confirmation dialog appears.
    * Confirm the deletion. Verify you are logged out and sent back to the `AuthScreen`.
    * Check the Firebase Console to confirm the user account has been removed.

## 7. Mandatory Development Guidelines

**These practices must be followed during all phases of development—planning, implementation, and review.**

### 1. Refer to the Style Guide
Before starting any feature:
- Always consult the **style guide** for rules related to UI/UX, layout, naming conventions, spacing, colors, and design patterns.
- The style guide is located in the **`style.md` file in the root folder`**.
- It is the **single source of truth** for styling decisions.

### 2. Study the Reference Project
Prior to implementation:
- Review the **reference project** located in the `uhabits-dev` folder (in the root directory).
- Understand how similar features have been approached to maintain consistency and avoid duplications or contradictions.
- The reference project serves as a **blueprint** for implementation.
- This step is mandatory. **Do not proceed to implementation without this step.**

### 3. Understand the Existing Project Structure
Before writing any code:
- Spend time exploring and understanding how the current system is structured.
- Even for new features, existing components or utility functions may be reusable.
- Integrate changes **cleanly into the existing architecture** instead of creating disconnected code.

### 4. Maintain a Clean Codebase
After implementing features:
- Remove any temporary, test, or duplicate files, folders, routes, or unused components that were created during development.
- Keep the codebase **organized and clutter-free**.

### 5. Pause If There Is Any Confusion
If at any point the requirements are unclear:
- **Do not proceed** based on assumptions.
- Immediately pause and seek clarification either from the project lead or directly from me.
- It is better to get clarity than to redo or fix avoidable mistakes later.

### 6. Remove Unused Old Implementations
As part of final review:
- Identify and delete **any old, unused code** that was implemented earlier but is no longer in use.
- This includes obsolete routes, modules, features, or legacy logic.